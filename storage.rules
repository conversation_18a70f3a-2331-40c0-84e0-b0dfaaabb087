rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Default deny all
    match /{allPaths=**} {
      allow read, write: if false;
    }

    // Documents storage with optimized validation (removed Firestore lookups to prevent ANR)
    match /documents/{allPaths=**} {
      // Allow read for authenticated users
      allow read: if request.auth != null;

      // Allow create with validation (simplified to prevent ANR)
      allow create: if request.auth != null
                    && request.resource.size <= 15 * 1024 * 1024 // 15MB limit
                    && request.resource.contentType.matches(
                        // Document formats
                        'application/pdf|application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|text/plain|' +
                        // Spreadsheet formats
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|application/vnd.ms-excel|text/csv|application/csv|text/comma-separated-values|application/vnd.oasis.opendocument.spreadsheet|' +
                        // Presentation formats
                        'application/vnd.openxmlformats-officedocument.presentationml.presentation|application/vnd.ms-powerpoint|' +
                        // Image formats
                        'image/jpeg|image/png|image/gif'
                      )
                    && request.resource.metadata.uploadedBy == request.auth.uid;

      // Allow update for document owner (admin check moved to client-side to prevent ANR)
      allow update: if request.auth != null
                    && resource.metadata.uploadedBy == request.auth.uid;

      // ADMIN-ONLY DELETE RULES: Only admin users can delete files
      allow delete: if request.auth != null
                    && request.auth.token.admin == true;
    }

    // User-specific documents folder (new structure)
    match /documents/{userId}/{fileName} {
      // Allow read for authenticated users
      allow read: if request.auth != null;

      // Allow create/update for the file owner
      allow create, update: if request.auth != null
                            && request.auth.uid == userId;

      // Allow delete ONLY for admin users
      allow delete: if request.auth != null
                    && request.auth.token.admin == true;
    }

    // Profile images storage
    match /profile_images/{userId}/{allPaths=**} {
      // Allow read for authenticated users
      allow read: if request.auth != null;

      // Allow write for the user's own profile image
      allow write: if request.auth != null
                   && request.auth.uid == userId
                   && request.resource.size <= 2 * 1024 * 1024 // 2MB limit
                   && request.resource.contentType.matches('image/jpeg|image/png|image/gif');
    }
  }
}