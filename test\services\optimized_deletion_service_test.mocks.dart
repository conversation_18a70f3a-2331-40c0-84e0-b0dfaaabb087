// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in managementdoc/test/services/optimized_deletion_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:cloud_firestore/cloud_firestore.dart' as _i4;
import 'package:firebase_auth/firebase_auth.dart' as _i3;
import 'package:firebase_storage/firebase_storage.dart' as _i5;
import 'package:managementdoc/core/services/document_service.dart' as _i8;
import 'package:managementdoc/core/services/firebase_service.dart' as _i10;
import 'package:managementdoc/models/document_model.dart' as _i7;
import 'package:managementdoc/services/direct_storage_deletion_service.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeStorageDeletionResult_0 extends _i1.SmartFake
    implements _i2.StorageDeletionResult {
  _FakeStorageDeletionResult_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBatchDeletionResult_1 extends _i1.SmartFake
    implements _i2.BatchDeletionResult {
  _FakeBatchDeletionResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeStorageFileInfo_2 extends _i1.SmartFake
    implements _i2.StorageFileInfo {
  _FakeStorageFileInfo_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAuth_3 extends _i1.SmartFake implements _i3.FirebaseAuth {
  _FakeFirebaseAuth_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseFirestore_4 extends _i1.SmartFake
    implements _i4.FirebaseFirestore {
  _FakeFirebaseFirestore_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseStorage_5 extends _i1.SmartFake
    implements _i5.FirebaseStorage {
  _FakeFirebaseStorage_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCollectionReference_6<T extends Object?> extends _i1.SmartFake
    implements _i4.CollectionReference<T> {
  _FakeCollectionReference_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeReference_7 extends _i1.SmartFake implements _i5.Reference {
  _FakeReference_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeWriteBatch_8 extends _i1.SmartFake implements _i4.WriteBatch {
  _FakeWriteBatch_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFieldValue_9 extends _i1.SmartFake implements _i4.FieldValue {
  _FakeFieldValue_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFuture_10<T1> extends _i1.SmartFake implements _i6.Future<T1> {
  _FakeFuture_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [DirectStorageDeletionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDirectStorageDeletionService extends _i1.Mock
    implements _i2.DirectStorageDeletionService {
  MockDirectStorageDeletionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.StorageDeletionResult> deleteFileByUrl(
    String? downloadUrl, {
    bool? forceDelete = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteFileByUrl,
              [downloadUrl],
              {#forceDelete: forceDelete},
            ),
            returnValue: _i6.Future<_i2.StorageDeletionResult>.value(
              _FakeStorageDeletionResult_0(
                this,
                Invocation.method(
                  #deleteFileByUrl,
                  [downloadUrl],
                  {#forceDelete: forceDelete},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.StorageDeletionResult>);

  @override
  _i6.Future<_i2.StorageDeletionResult> deleteDocumentDirect(
    _i7.DocumentModel? document, {
    bool? forceDelete = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteDocumentDirect,
              [document],
              {#forceDelete: forceDelete},
            ),
            returnValue: _i6.Future<_i2.StorageDeletionResult>.value(
              _FakeStorageDeletionResult_0(
                this,
                Invocation.method(
                  #deleteDocumentDirect,
                  [document],
                  {#forceDelete: forceDelete},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.StorageDeletionResult>);

  @override
  _i6.Future<_i2.StorageDeletionResult> deleteFileByPath(
    String? storagePath, {
    bool? forceDelete = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteFileByPath,
              [storagePath],
              {#forceDelete: forceDelete},
            ),
            returnValue: _i6.Future<_i2.StorageDeletionResult>.value(
              _FakeStorageDeletionResult_0(
                this,
                Invocation.method(
                  #deleteFileByPath,
                  [storagePath],
                  {#forceDelete: forceDelete},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.StorageDeletionResult>);

  @override
  _i6.Future<_i2.BatchDeletionResult> deleteMultipleFilesByUrl(
    List<String>? downloadUrls, {
    bool? continueOnError = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteMultipleFilesByUrl,
              [downloadUrls],
              {#continueOnError: continueOnError},
            ),
            returnValue: _i6.Future<_i2.BatchDeletionResult>.value(
              _FakeBatchDeletionResult_1(
                this,
                Invocation.method(
                  #deleteMultipleFilesByUrl,
                  [downloadUrls],
                  {#continueOnError: continueOnError},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.BatchDeletionResult>);

  @override
  _i6.Future<_i2.BatchDeletionResult> deleteMultipleDocumentsDirect(
    List<_i7.DocumentModel>? documents, {
    bool? continueOnError = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteMultipleDocumentsDirect,
              [documents],
              {#continueOnError: continueOnError},
            ),
            returnValue: _i6.Future<_i2.BatchDeletionResult>.value(
              _FakeBatchDeletionResult_1(
                this,
                Invocation.method(
                  #deleteMultipleDocumentsDirect,
                  [documents],
                  {#continueOnError: continueOnError},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.BatchDeletionResult>);

  @override
  _i6.Future<_i2.BatchDeletionResult> deleteMultipleFilesByPath(
    List<String>? storagePaths, {
    bool? continueOnError = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteMultipleFilesByPath,
              [storagePaths],
              {#continueOnError: continueOnError},
            ),
            returnValue: _i6.Future<_i2.BatchDeletionResult>.value(
              _FakeBatchDeletionResult_1(
                this,
                Invocation.method(
                  #deleteMultipleFilesByPath,
                  [storagePaths],
                  {#continueOnError: continueOnError},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.BatchDeletionResult>);

  @override
  _i6.Future<_i2.BatchDeletionResult> deleteFilesByPattern(
    String? filenamePattern, {
    String? searchPath = 'documents',
    bool? exactMatch = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteFilesByPattern,
              [filenamePattern],
              {#searchPath: searchPath, #exactMatch: exactMatch},
            ),
            returnValue: _i6.Future<_i2.BatchDeletionResult>.value(
              _FakeBatchDeletionResult_1(
                this,
                Invocation.method(
                  #deleteFilesByPattern,
                  [filenamePattern],
                  {#searchPath: searchPath, #exactMatch: exactMatch},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.BatchDeletionResult>);

  @override
  _i6.Future<_i2.StorageFileInfo> getFileInfo(String? storagePath) =>
      (super.noSuchMethod(
            Invocation.method(#getFileInfo, [storagePath]),
            returnValue: _i6.Future<_i2.StorageFileInfo>.value(
              _FakeStorageFileInfo_2(
                this,
                Invocation.method(#getFileInfo, [storagePath]),
              ),
            ),
          )
          as _i6.Future<_i2.StorageFileInfo>);
}

/// A class which mocks [DocumentService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDocumentService extends _i1.Mock implements _i8.DocumentService {
  MockDocumentService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<List<_i7.DocumentModel>> getAllDocuments({
    int? limit,
    _i4.DocumentSnapshot<Object?>? startAfter,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllDocuments, [], {
              #limit: limit,
              #startAfter: startAfter,
            }),
            returnValue: _i6.Future<List<_i7.DocumentModel>>.value(
              <_i7.DocumentModel>[],
            ),
          )
          as _i6.Future<List<_i7.DocumentModel>>);

  @override
  _i6.Future<_i7.DocumentModel?> getDocumentById(String? documentId) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentById, [documentId]),
            returnValue: _i6.Future<_i7.DocumentModel?>.value(),
          )
          as _i6.Future<_i7.DocumentModel?>);

  @override
  _i6.Future<String> addDocument(_i7.DocumentModel? document) =>
      (super.noSuchMethod(
            Invocation.method(#addDocument, [document]),
            returnValue: _i6.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#addDocument, [document]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<String> addDocumentSilent(_i7.DocumentModel? document) =>
      (super.noSuchMethod(
            Invocation.method(#addDocumentSilent, [document]),
            returnValue: _i6.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#addDocumentSilent, [document]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<void> updateDocument(_i7.DocumentModel? document) =>
      (super.noSuchMethod(
            Invocation.method(#updateDocument, [document]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> deleteDocument(String? documentId, String? deletedBy) =>
      (super.noSuchMethod(
            Invocation.method(#deleteDocument, [documentId, deletedBy]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> deleteDocumentFromStorage(String? filePath) =>
      (super.noSuchMethod(
            Invocation.method(#deleteDocumentFromStorage, [filePath]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<List<_i7.DocumentModel>> getDocumentsByCategory(
    String? categoryId, {
    int? limit,
    _i4.DocumentSnapshot<Object?>? startAfter,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getDocumentsByCategory,
              [categoryId],
              {#limit: limit, #startAfter: startAfter},
            ),
            returnValue: _i6.Future<List<_i7.DocumentModel>>.value(
              <_i7.DocumentModel>[],
            ),
          )
          as _i6.Future<List<_i7.DocumentModel>>);

  @override
  _i6.Future<List<_i7.DocumentModel>> getDocumentsByUser(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentsByUser, [userId]),
            returnValue: _i6.Future<List<_i7.DocumentModel>>.value(
              <_i7.DocumentModel>[],
            ),
          )
          as _i6.Future<List<_i7.DocumentModel>>);

  @override
  _i6.Future<void> updateDocumentCategory(
    String? documentId,
    String? newCategoryId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateDocumentCategory, [
              documentId,
              newCategoryId,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<List<_i7.DocumentModel>> searchDocuments(
    String? query, {
    int? limit,
    _i4.DocumentSnapshot<Object?>? startAfter,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #searchDocuments,
              [query],
              {#limit: limit, #startAfter: startAfter},
            ),
            returnValue: _i6.Future<List<_i7.DocumentModel>>.value(
              <_i7.DocumentModel>[],
            ),
          )
          as _i6.Future<List<_i7.DocumentModel>>);

  @override
  _i6.Future<List<_i7.DocumentModel>> getRecentDocuments({
    int? limit = 10,
    _i4.DocumentSnapshot<Object?>? startAfter,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentDocuments, [], {
              #limit: limit,
              #startAfter: startAfter,
            }),
            returnValue: _i6.Future<List<_i7.DocumentModel>>.value(
              <_i7.DocumentModel>[],
            ),
          )
          as _i6.Future<List<_i7.DocumentModel>>);

  @override
  _i6.Future<void> forceDeleteFromStorage(
    String? documentId,
    String? fileName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#forceDeleteFromStorage, [documentId, fileName]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [FirebaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseService extends _i1.Mock implements _i10.FirebaseService {
  MockFirebaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.FirebaseAuth get auth =>
      (super.noSuchMethod(
            Invocation.getter(#auth),
            returnValue: _FakeFirebaseAuth_3(this, Invocation.getter(#auth)),
          )
          as _i3.FirebaseAuth);

  @override
  _i4.FirebaseFirestore get firestore =>
      (super.noSuchMethod(
            Invocation.getter(#firestore),
            returnValue: _FakeFirebaseFirestore_4(
              this,
              Invocation.getter(#firestore),
            ),
          )
          as _i4.FirebaseFirestore);

  @override
  _i5.FirebaseStorage get storage =>
      (super.noSuchMethod(
            Invocation.getter(#storage),
            returnValue: _FakeFirebaseStorage_5(
              this,
              Invocation.getter(#storage),
            ),
          )
          as _i5.FirebaseStorage);

  @override
  _i4.CollectionReference<Object?> get usersCollection =>
      (super.noSuchMethod(
            Invocation.getter(#usersCollection),
            returnValue: _FakeCollectionReference_6<Object?>(
              this,
              Invocation.getter(#usersCollection),
            ),
          )
          as _i4.CollectionReference<Object?>);

  @override
  _i4.CollectionReference<Object?> get documentsCollection =>
      (super.noSuchMethod(
            Invocation.getter(#documentsCollection),
            returnValue: _FakeCollectionReference_6<Object?>(
              this,
              Invocation.getter(#documentsCollection),
            ),
          )
          as _i4.CollectionReference<Object?>);

  @override
  _i4.CollectionReference<Object?> get activitiesCollection =>
      (super.noSuchMethod(
            Invocation.getter(#activitiesCollection),
            returnValue: _FakeCollectionReference_6<Object?>(
              this,
              Invocation.getter(#activitiesCollection),
            ),
          )
          as _i4.CollectionReference<Object?>);

  @override
  _i4.CollectionReference<Object?> get categoriesCollection =>
      (super.noSuchMethod(
            Invocation.getter(#categoriesCollection),
            returnValue: _FakeCollectionReference_6<Object?>(
              this,
              Invocation.getter(#categoriesCollection),
            ),
          )
          as _i4.CollectionReference<Object?>);

  @override
  _i5.Reference get documentsStorage =>
      (super.noSuchMethod(
            Invocation.getter(#documentsStorage),
            returnValue: _FakeReference_7(
              this,
              Invocation.getter(#documentsStorage),
            ),
          )
          as _i5.Reference);

  @override
  _i5.Reference get profileImagesStorage =>
      (super.noSuchMethod(
            Invocation.getter(#profileImagesStorage),
            returnValue: _FakeReference_7(
              this,
              Invocation.getter(#profileImagesStorage),
            ),
          )
          as _i5.Reference);

  @override
  _i4.WriteBatch get batch =>
      (super.noSuchMethod(
            Invocation.getter(#batch),
            returnValue: _FakeWriteBatch_8(this, Invocation.getter(#batch)),
          )
          as _i4.WriteBatch);

  @override
  _i4.FieldValue get serverTimestamp =>
      (super.noSuchMethod(
            Invocation.getter(#serverTimestamp),
            returnValue: _FakeFieldValue_9(
              this,
              Invocation.getter(#serverTimestamp),
            ),
          )
          as _i4.FieldValue);

  @override
  _i6.Future<T> runTransaction<T>(
    _i6.Future<T> Function(_i4.Transaction)? updateFunction,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#runTransaction, [updateFunction]),
            returnValue:
                _i9.ifNotNull(
                  _i9.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#runTransaction, [updateFunction]),
                  ),
                  (T v) => _i6.Future<T>.value(v),
                ) ??
                _FakeFuture_10<T>(
                  this,
                  Invocation.method(#runTransaction, [updateFunction]),
                ),
          )
          as _i6.Future<T>);

  @override
  _i6.Future<bool> checkConnection() =>
      (super.noSuchMethod(
            Invocation.method(#checkConnection, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<void> enableNetwork() =>
      (super.noSuchMethod(
            Invocation.method(#enableNetwork, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> disableNetwork() =>
      (super.noSuchMethod(
            Invocation.method(#disableNetwork, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> clearPersistence() =>
      (super.noSuchMethod(
            Invocation.method(#clearPersistence, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> terminate() =>
      (super.noSuchMethod(
            Invocation.method(#terminate, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}
