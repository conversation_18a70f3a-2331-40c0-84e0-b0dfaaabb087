// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in managementdoc/test/services/direct_storage_deletion_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:io' as _i11;
import 'dart:typed_data' as _i10;

import 'package:cloud_firestore/cloud_firestore.dart' as _i3;
import 'package:firebase_auth/firebase_auth.dart' as _i2;
import 'package:firebase_core/firebase_core.dart' as _i6;
import 'package:firebase_storage/firebase_storage.dart' as _i4;
import 'package:managementdoc/core/services/firebase_service.dart' as _i7;
import 'package:managementdoc/services/admin_permission_service.dart' as _i9;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseAuth_0 extends _i1.SmartFake implements _i2.FirebaseAuth {
  _FakeFirebaseAuth_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseFirestore_1 extends _i1.SmartFake
    implements _i3.FirebaseFirestore {
  _FakeFirebaseFirestore_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseStorage_2 extends _i1.SmartFake
    implements _i4.FirebaseStorage {
  _FakeFirebaseStorage_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCollectionReference_3<T extends Object?> extends _i1.SmartFake
    implements _i3.CollectionReference<T> {
  _FakeCollectionReference_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeReference_4 extends _i1.SmartFake implements _i4.Reference {
  _FakeReference_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeWriteBatch_5 extends _i1.SmartFake implements _i3.WriteBatch {
  _FakeWriteBatch_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFieldValue_6 extends _i1.SmartFake implements _i3.FieldValue {
  _FakeFieldValue_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFuture_7<T1> extends _i1.SmartFake implements _i5.Future<T1> {
  _FakeFuture_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseApp_8 extends _i1.SmartFake implements _i6.FirebaseApp {
  _FakeFirebaseApp_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDuration_9 extends _i1.SmartFake implements Duration {
  _FakeDuration_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFullMetadata_10 extends _i1.SmartFake implements _i4.FullMetadata {
  _FakeFullMetadata_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeListResult_11 extends _i1.SmartFake implements _i4.ListResult {
  _FakeListResult_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUploadTask_12 extends _i1.SmartFake implements _i4.UploadTask {
  _FakeUploadTask_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDownloadTask_13 extends _i1.SmartFake implements _i4.DownloadTask {
  _FakeDownloadTask_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirebaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseService extends _i1.Mock implements _i7.FirebaseService {
  MockFirebaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseAuth get auth =>
      (super.noSuchMethod(
            Invocation.getter(#auth),
            returnValue: _FakeFirebaseAuth_0(this, Invocation.getter(#auth)),
          )
          as _i2.FirebaseAuth);

  @override
  _i3.FirebaseFirestore get firestore =>
      (super.noSuchMethod(
            Invocation.getter(#firestore),
            returnValue: _FakeFirebaseFirestore_1(
              this,
              Invocation.getter(#firestore),
            ),
          )
          as _i3.FirebaseFirestore);

  @override
  _i4.FirebaseStorage get storage =>
      (super.noSuchMethod(
            Invocation.getter(#storage),
            returnValue: _FakeFirebaseStorage_2(
              this,
              Invocation.getter(#storage),
            ),
          )
          as _i4.FirebaseStorage);

  @override
  _i3.CollectionReference<Object?> get usersCollection =>
      (super.noSuchMethod(
            Invocation.getter(#usersCollection),
            returnValue: _FakeCollectionReference_3<Object?>(
              this,
              Invocation.getter(#usersCollection),
            ),
          )
          as _i3.CollectionReference<Object?>);

  @override
  _i3.CollectionReference<Object?> get documentsCollection =>
      (super.noSuchMethod(
            Invocation.getter(#documentsCollection),
            returnValue: _FakeCollectionReference_3<Object?>(
              this,
              Invocation.getter(#documentsCollection),
            ),
          )
          as _i3.CollectionReference<Object?>);

  @override
  _i3.CollectionReference<Object?> get activitiesCollection =>
      (super.noSuchMethod(
            Invocation.getter(#activitiesCollection),
            returnValue: _FakeCollectionReference_3<Object?>(
              this,
              Invocation.getter(#activitiesCollection),
            ),
          )
          as _i3.CollectionReference<Object?>);

  @override
  _i3.CollectionReference<Object?> get categoriesCollection =>
      (super.noSuchMethod(
            Invocation.getter(#categoriesCollection),
            returnValue: _FakeCollectionReference_3<Object?>(
              this,
              Invocation.getter(#categoriesCollection),
            ),
          )
          as _i3.CollectionReference<Object?>);

  @override
  _i4.Reference get documentsStorage =>
      (super.noSuchMethod(
            Invocation.getter(#documentsStorage),
            returnValue: _FakeReference_4(
              this,
              Invocation.getter(#documentsStorage),
            ),
          )
          as _i4.Reference);

  @override
  _i4.Reference get profileImagesStorage =>
      (super.noSuchMethod(
            Invocation.getter(#profileImagesStorage),
            returnValue: _FakeReference_4(
              this,
              Invocation.getter(#profileImagesStorage),
            ),
          )
          as _i4.Reference);

  @override
  _i3.WriteBatch get batch =>
      (super.noSuchMethod(
            Invocation.getter(#batch),
            returnValue: _FakeWriteBatch_5(this, Invocation.getter(#batch)),
          )
          as _i3.WriteBatch);

  @override
  _i3.FieldValue get serverTimestamp =>
      (super.noSuchMethod(
            Invocation.getter(#serverTimestamp),
            returnValue: _FakeFieldValue_6(
              this,
              Invocation.getter(#serverTimestamp),
            ),
          )
          as _i3.FieldValue);

  @override
  _i5.Future<T> runTransaction<T>(
    _i5.Future<T> Function(_i3.Transaction)? updateFunction,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#runTransaction, [updateFunction]),
            returnValue:
                _i8.ifNotNull(
                  _i8.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#runTransaction, [updateFunction]),
                  ),
                  (T v) => _i5.Future<T>.value(v),
                ) ??
                _FakeFuture_7<T>(
                  this,
                  Invocation.method(#runTransaction, [updateFunction]),
                ),
          )
          as _i5.Future<T>);

  @override
  _i5.Future<bool> checkConnection() =>
      (super.noSuchMethod(
            Invocation.method(#checkConnection, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> enableNetwork() =>
      (super.noSuchMethod(
            Invocation.method(#enableNetwork, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> disableNetwork() =>
      (super.noSuchMethod(
            Invocation.method(#disableNetwork, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> clearPersistence() =>
      (super.noSuchMethod(
            Invocation.method(#clearPersistence, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> terminate() =>
      (super.noSuchMethod(
            Invocation.method(#terminate, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [AdminPermissionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdminPermissionService extends _i1.Mock
    implements _i9.AdminPermissionService {
  MockAdminPermissionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<bool> isCurrentUserAdmin() =>
      (super.noSuchMethod(
            Invocation.method(#isCurrentUserAdmin, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> isUserAdmin(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#isUserAdmin, [userId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canDeleteFile(String? fileOwnerId) =>
      (super.noSuchMethod(
            Invocation.method(#canDeleteFile, [fileOwnerId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canUpdateFile(String? fileOwnerId) =>
      (super.noSuchMethod(
            Invocation.method(#canUpdateFile, [fileOwnerId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canManageCategories() =>
      (super.noSuchMethod(
            Invocation.method(#canManageCategories, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canManageUsers() =>
      (super.noSuchMethod(
            Invocation.method(#canManageUsers, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canViewAllFiles() =>
      (super.noSuchMethod(
            Invocation.method(#canViewAllFiles, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<Map<String, bool>> batchCheckAdminStatus(List<String>? userIds) =>
      (super.noSuchMethod(
            Invocation.method(#batchCheckAdminStatus, [userIds]),
            returnValue: _i5.Future<Map<String, bool>>.value(<String, bool>{}),
          )
          as _i5.Future<Map<String, bool>>);

  @override
  void clearAdminCache(String? userId) => super.noSuchMethod(
    Invocation.method(#clearAdminCache, [userId]),
    returnValueForMissingStub: null,
  );

  @override
  void clearAllAdminCache() => super.noSuchMethod(
    Invocation.method(#clearAllAdminCache, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<bool> refreshCurrentUserAdminStatus() =>
      (super.noSuchMethod(
            Invocation.method(#refreshCurrentUserAdminStatus, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  Map<String, dynamic> getAdminCacheStats() =>
      (super.noSuchMethod(
            Invocation.method(#getAdminCacheStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void cleanupExpiredCache() => super.noSuchMethod(
    Invocation.method(#cleanupExpiredCache, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FirebaseStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseStorage extends _i1.Mock implements _i4.FirebaseStorage {
  MockFirebaseStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_8(this, Invocation.getter(#app)),
          )
          as _i6.FirebaseApp);

  @override
  String get bucket =>
      (super.noSuchMethod(
            Invocation.getter(#bucket),
            returnValue: _i8.dummyValue<String>(
              this,
              Invocation.getter(#bucket),
            ),
          )
          as String);

  @override
  Duration get maxOperationRetryTime =>
      (super.noSuchMethod(
            Invocation.getter(#maxOperationRetryTime),
            returnValue: _FakeDuration_9(
              this,
              Invocation.getter(#maxOperationRetryTime),
            ),
          )
          as Duration);

  @override
  Duration get maxUploadRetryTime =>
      (super.noSuchMethod(
            Invocation.getter(#maxUploadRetryTime),
            returnValue: _FakeDuration_9(
              this,
              Invocation.getter(#maxUploadRetryTime),
            ),
          )
          as Duration);

  @override
  Duration get maxDownloadRetryTime =>
      (super.noSuchMethod(
            Invocation.getter(#maxDownloadRetryTime),
            returnValue: _FakeDuration_9(
              this,
              Invocation.getter(#maxDownloadRetryTime),
            ),
          )
          as Duration);

  @override
  set app(_i6.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  set bucket(String? _bucket) => super.noSuchMethod(
    Invocation.setter(#bucket, _bucket),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i4.Reference ref([String? path]) =>
      (super.noSuchMethod(
            Invocation.method(#ref, [path]),
            returnValue: _FakeReference_4(
              this,
              Invocation.method(#ref, [path]),
            ),
          )
          as _i4.Reference);

  @override
  _i4.Reference refFromURL(String? url) =>
      (super.noSuchMethod(
            Invocation.method(#refFromURL, [url]),
            returnValue: _FakeReference_4(
              this,
              Invocation.method(#refFromURL, [url]),
            ),
          )
          as _i4.Reference);

  @override
  void setMaxOperationRetryTime(Duration? time) => super.noSuchMethod(
    Invocation.method(#setMaxOperationRetryTime, [time]),
    returnValueForMissingStub: null,
  );

  @override
  void setMaxUploadRetryTime(Duration? time) => super.noSuchMethod(
    Invocation.method(#setMaxUploadRetryTime, [time]),
    returnValueForMissingStub: null,
  );

  @override
  void setMaxDownloadRetryTime(Duration? time) => super.noSuchMethod(
    Invocation.method(#setMaxDownloadRetryTime, [time]),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> useStorageEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #useStorageEmulator,
              [host, port],
              {#automaticHostMapping: automaticHostMapping},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [Reference].
///
/// See the documentation for Mockito's code generation for more information.
class MockReference extends _i1.Mock implements _i4.Reference {
  MockReference() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.FirebaseStorage get storage =>
      (super.noSuchMethod(
            Invocation.getter(#storage),
            returnValue: _FakeFirebaseStorage_2(
              this,
              Invocation.getter(#storage),
            ),
          )
          as _i4.FirebaseStorage);

  @override
  String get bucket =>
      (super.noSuchMethod(
            Invocation.getter(#bucket),
            returnValue: _i8.dummyValue<String>(
              this,
              Invocation.getter(#bucket),
            ),
          )
          as String);

  @override
  String get fullPath =>
      (super.noSuchMethod(
            Invocation.getter(#fullPath),
            returnValue: _i8.dummyValue<String>(
              this,
              Invocation.getter(#fullPath),
            ),
          )
          as String);

  @override
  String get name =>
      (super.noSuchMethod(
            Invocation.getter(#name),
            returnValue: _i8.dummyValue<String>(this, Invocation.getter(#name)),
          )
          as String);

  @override
  _i4.Reference get root =>
      (super.noSuchMethod(
            Invocation.getter(#root),
            returnValue: _FakeReference_4(this, Invocation.getter(#root)),
          )
          as _i4.Reference);

  @override
  _i4.Reference child(String? path) =>
      (super.noSuchMethod(
            Invocation.method(#child, [path]),
            returnValue: _FakeReference_4(
              this,
              Invocation.method(#child, [path]),
            ),
          )
          as _i4.Reference);

  @override
  _i5.Future<void> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<String> getDownloadURL() =>
      (super.noSuchMethod(
            Invocation.method(#getDownloadURL, []),
            returnValue: _i5.Future<String>.value(
              _i8.dummyValue<String>(
                this,
                Invocation.method(#getDownloadURL, []),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<_i4.FullMetadata> getMetadata() =>
      (super.noSuchMethod(
            Invocation.method(#getMetadata, []),
            returnValue: _i5.Future<_i4.FullMetadata>.value(
              _FakeFullMetadata_10(this, Invocation.method(#getMetadata, [])),
            ),
          )
          as _i5.Future<_i4.FullMetadata>);

  @override
  _i5.Future<_i4.ListResult> list([_i4.ListOptions? options]) =>
      (super.noSuchMethod(
            Invocation.method(#list, [options]),
            returnValue: _i5.Future<_i4.ListResult>.value(
              _FakeListResult_11(this, Invocation.method(#list, [options])),
            ),
          )
          as _i5.Future<_i4.ListResult>);

  @override
  _i5.Future<_i4.ListResult> listAll() =>
      (super.noSuchMethod(
            Invocation.method(#listAll, []),
            returnValue: _i5.Future<_i4.ListResult>.value(
              _FakeListResult_11(this, Invocation.method(#listAll, [])),
            ),
          )
          as _i5.Future<_i4.ListResult>);

  @override
  _i5.Future<_i10.Uint8List?> getData([int? maxSize = 10485760]) =>
      (super.noSuchMethod(
            Invocation.method(#getData, [maxSize]),
            returnValue: _i5.Future<_i10.Uint8List?>.value(),
          )
          as _i5.Future<_i10.Uint8List?>);

  @override
  _i4.UploadTask putData(
    _i10.Uint8List? data, [
    _i4.SettableMetadata? metadata,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#putData, [data, metadata]),
            returnValue: _FakeUploadTask_12(
              this,
              Invocation.method(#putData, [data, metadata]),
            ),
          )
          as _i4.UploadTask);

  @override
  _i4.UploadTask putBlob(dynamic blob, [_i4.SettableMetadata? metadata]) =>
      (super.noSuchMethod(
            Invocation.method(#putBlob, [blob, metadata]),
            returnValue: _FakeUploadTask_12(
              this,
              Invocation.method(#putBlob, [blob, metadata]),
            ),
          )
          as _i4.UploadTask);

  @override
  _i4.UploadTask putFile(_i11.File? file, [_i4.SettableMetadata? metadata]) =>
      (super.noSuchMethod(
            Invocation.method(#putFile, [file, metadata]),
            returnValue: _FakeUploadTask_12(
              this,
              Invocation.method(#putFile, [file, metadata]),
            ),
          )
          as _i4.UploadTask);

  @override
  _i4.UploadTask putString(
    String? data, {
    _i4.PutStringFormat? format = _i4.PutStringFormat.raw,
    _i4.SettableMetadata? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #putString,
              [data],
              {#format: format, #metadata: metadata},
            ),
            returnValue: _FakeUploadTask_12(
              this,
              Invocation.method(
                #putString,
                [data],
                {#format: format, #metadata: metadata},
              ),
            ),
          )
          as _i4.UploadTask);

  @override
  _i5.Future<_i4.FullMetadata> updateMetadata(_i4.SettableMetadata? metadata) =>
      (super.noSuchMethod(
            Invocation.method(#updateMetadata, [metadata]),
            returnValue: _i5.Future<_i4.FullMetadata>.value(
              _FakeFullMetadata_10(
                this,
                Invocation.method(#updateMetadata, [metadata]),
              ),
            ),
          )
          as _i5.Future<_i4.FullMetadata>);

  @override
  _i4.DownloadTask writeToFile(_i11.File? file) =>
      (super.noSuchMethod(
            Invocation.method(#writeToFile, [file]),
            returnValue: _FakeDownloadTask_13(
              this,
              Invocation.method(#writeToFile, [file]),
            ),
          )
          as _i4.DownloadTask);
}

/// A class which mocks [FullMetadata].
///
/// See the documentation for Mockito's code generation for more information.
class MockFullMetadata extends _i1.Mock implements _i4.FullMetadata {
  MockFullMetadata() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get fullPath =>
      (super.noSuchMethod(
            Invocation.getter(#fullPath),
            returnValue: _i8.dummyValue<String>(
              this,
              Invocation.getter(#fullPath),
            ),
          )
          as String);

  @override
  String get name =>
      (super.noSuchMethod(
            Invocation.getter(#name),
            returnValue: _i8.dummyValue<String>(this, Invocation.getter(#name)),
          )
          as String);
}
