// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in managementdoc/test/home_screen_file_loading_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:ui' as _i6;

import 'package:managementdoc/models/document_model.dart' as _i3;
import 'package:managementdoc/models/user_model.dart' as _i8;
import 'package:managementdoc/providers/auth_provider.dart' as _i7;
import 'package:managementdoc/providers/document_provider.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [DocumentProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockDocumentProvider extends _i1.Mock implements _i2.DocumentProvider {
  MockDocumentProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  List<_i3.DocumentModel> get documents =>
      (super.noSuchMethod(
            Invocation.getter(#documents),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  List<_i3.DocumentModel> get allDocuments =>
      (super.noSuchMethod(
            Invocation.getter(#allDocuments),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  List<_i3.DocumentModel> get filteredDocuments =>
      (super.noSuchMethod(
            Invocation.getter(#filteredDocuments),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  String get searchQuery =>
      (super.noSuchMethod(
            Invocation.getter(#searchQuery),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#searchQuery),
            ),
          )
          as String);

  @override
  String get selectedFileType =>
      (super.noSuchMethod(
            Invocation.getter(#selectedFileType),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#selectedFileType),
            ),
          )
          as String);

  @override
  String get selectedCategory =>
      (super.noSuchMethod(
            Invocation.getter(#selectedCategory),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#selectedCategory),
            ),
          )
          as String);

  @override
  String get selectedStatus =>
      (super.noSuchMethod(
            Invocation.getter(#selectedStatus),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#selectedStatus),
            ),
          )
          as String);

  @override
  String get sortBy =>
      (super.noSuchMethod(
            Invocation.getter(#sortBy),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#sortBy),
            ),
          )
          as String);

  @override
  bool get sortAscending =>
      (super.noSuchMethod(Invocation.getter(#sortAscending), returnValue: false)
          as bool);

  @override
  bool get isFirebaseSyncActive =>
      (super.noSuchMethod(
            Invocation.getter(#isFirebaseSyncActive),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasActiveFilters =>
      (super.noSuchMethod(
            Invocation.getter(#hasActiveFilters),
            returnValue: false,
          )
          as bool);

  @override
  int get totalDocumentsCount =>
      (super.noSuchMethod(
            Invocation.getter(#totalDocumentsCount),
            returnValue: 0,
          )
          as int);

  @override
  _i5.Future<bool> get canUseUnlimitedQueries =>
      (super.noSuchMethod(
            Invocation.getter(#canUseUnlimitedQueries),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> get canManageStorage =>
      (super.noSuchMethod(
            Invocation.getter(#canManageStorage),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  int get totalFileSize =>
      (super.noSuchMethod(Invocation.getter(#totalFileSize), returnValue: 0)
          as int);

  @override
  String get totalFileSizeFormatted =>
      (super.noSuchMethod(
            Invocation.getter(#totalFileSizeFormatted),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#totalFileSizeFormatted),
            ),
          )
          as String);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i5.Future<void> syncWithUnifiedLoader() =>
      (super.noSuchMethod(
            Invocation.method(#syncWithUnifiedLoader, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> loadDocuments({bool? forceRefresh = false}) =>
      (super.noSuchMethod(
            Invocation.method(#loadDocuments, [], {
              #forceRefresh: forceRefresh,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void addDocument(_i3.DocumentModel? document) => super.noSuchMethod(
    Invocation.method(#addDocument, [document]),
    returnValueForMissingStub: null,
  );

  @override
  void addDocumentToCategory(_i3.DocumentModel? document, String? categoryId) =>
      super.noSuchMethod(
        Invocation.method(#addDocumentToCategory, [document, categoryId]),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<void> updateMultipleDocumentsCategory(
    List<String>? documentIds,
    String? categoryId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateMultipleDocumentsCategory, [
              documentIds,
              categoryId,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateDocumentCategory(
    String? documentId,
    String? categoryId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateDocumentCategory, [
              documentId,
              categoryId,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> removeFileFromCategory(
    String? documentId,
    String? categoryId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#removeFileFromCategory, [
              documentId,
              categoryId,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void updateDocument(_i3.DocumentModel? document) => super.noSuchMethod(
    Invocation.method(#updateDocument, [document]),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> removeDocument(String? documentId, String? deletedBy) =>
      (super.noSuchMethod(
            Invocation.method(#removeDocument, [documentId, deletedBy]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void forceRemoveFromLocal(String? documentId) => super.noSuchMethod(
    Invocation.method(#forceRemoveFromLocal, [documentId]),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> forceRefreshDocuments() =>
      (super.noSuchMethod(
            Invocation.method(#forceRefreshDocuments, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> runFilePathDiagnostic() =>
      (super.noSuchMethod(
            Invocation.method(#runFilePathDiagnostic, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  void searchDocuments(String? query) => super.noSuchMethod(
    Invocation.method(#searchDocuments, [query]),
    returnValueForMissingStub: null,
  );

  @override
  void filterByCategory(String? category) => super.noSuchMethod(
    Invocation.method(#filterByCategory, [category]),
    returnValueForMissingStub: null,
  );

  @override
  void filterByStatus(String? status) => super.noSuchMethod(
    Invocation.method(#filterByStatus, [status]),
    returnValueForMissingStub: null,
  );

  @override
  void filterByFileType(String? fileType) => super.noSuchMethod(
    Invocation.method(#filterByFileType, [fileType]),
    returnValueForMissingStub: null,
  );

  @override
  void sortDocuments(String? sortBy, {bool? ascending = false}) =>
      super.noSuchMethod(
        Invocation.method(#sortDocuments, [sortBy], {#ascending: ascending}),
        returnValueForMissingStub: null,
      );

  @override
  void clearFilters() => super.noSuchMethod(
    Invocation.method(#clearFilters, []),
    returnValueForMissingStub: null,
  );

  @override
  _i3.DocumentModel? getDocumentById(String? documentId) =>
      (super.noSuchMethod(Invocation.method(#getDocumentById, [documentId]))
          as _i3.DocumentModel?);

  @override
  List<_i3.DocumentModel> getDocumentsByCategory(String? category) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentsByCategory, [category]),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  _i5.Future<List<_i3.DocumentModel>> getDocumentsByCategoryAsync(
    String? category,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentsByCategoryAsync, [category]),
            returnValue: _i5.Future<List<_i3.DocumentModel>>.value(
              <_i3.DocumentModel>[],
            ),
          )
          as _i5.Future<List<_i3.DocumentModel>>);

  @override
  List<_i3.DocumentModel> getRecentFiles({int? days = 7, int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentFiles, [], {
              #days: days,
              #limit: limit,
            }),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  List<_i3.DocumentModel> getUncategorizedFiles() =>
      (super.noSuchMethod(
            Invocation.method(#getUncategorizedFiles, []),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  void initializeCategory(String? categoryId) => super.noSuchMethod(
    Invocation.method(#initializeCategory, [categoryId]),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> refreshFolderContents() =>
      (super.noSuchMethod(
            Invocation.method(#refreshFolderContents, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void removeCategory(String? categoryId) => super.noSuchMethod(
    Invocation.method(#removeCategory, [categoryId]),
    returnValueForMissingStub: null,
  );

  @override
  List<_i3.DocumentModel> getDocumentsByStatus(String? status) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentsByStatus, [status]),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  List<_i3.DocumentModel> getDocumentsByUser(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentsByUser, [userId]),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  List<_i3.DocumentModel> getRecentDocuments({int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentDocuments, [], {#limit: limit}),
            returnValue: <_i3.DocumentModel>[],
          )
          as List<_i3.DocumentModel>);

  @override
  _i5.Future<void> refreshRecentFiles() =>
      (super.noSuchMethod(
            Invocation.method(#refreshRecentFiles, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> loadAllDocumentsUnlimited({
    String? categoryFilter,
    String? searchQuery,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loadAllDocumentsUnlimited, [], {
              #categoryFilter: categoryFilter,
              #searchQuery: searchQuery,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> loadDocumentsFromStorageUnlimited() =>
      (super.noSuchMethod(
            Invocation.method(#loadDocumentsFromStorageUnlimited, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> refreshAllDownloadUrls() =>
      (super.noSuchMethod(
            Invocation.method(#refreshAllDownloadUrls, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> getDocumentStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentStatistics, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> refreshDocuments() =>
      (super.noSuchMethod(
            Invocation.method(#refreshDocuments, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> refreshWithStorageSync() =>
      (super.noSuchMethod(
            Invocation.method(#refreshWithStorageSync, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void forceRefresh() => super.noSuchMethod(
    Invocation.method(#forceRefresh, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> cleanupDuplicateDocuments() =>
      (super.noSuchMethod(
            Invocation.method(#cleanupDuplicateDocuments, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> getSyncStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getSyncStatus, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i7.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get isLoggedIn =>
      (super.noSuchMethod(Invocation.getter(#isLoggedIn), returnValue: false)
          as bool);

  @override
  bool get isAdmin =>
      (super.noSuchMethod(Invocation.getter(#isAdmin), returnValue: false)
          as bool);

  @override
  _i5.Future<bool> get isCurrentUserAdmin =>
      (super.noSuchMethod(
            Invocation.getter(#isCurrentUserAdmin),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i5.Future<void> initializeAuth() =>
      (super.noSuchMethod(
            Invocation.method(#initializeAuth, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> login(
    String? email,
    String? password, {
    bool? rememberMe = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #login,
              [email, password],
              {#rememberMe: rememberMe},
            ),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> refreshCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshCurrentUser, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> resetPassword(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [email]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> changePassword(
    String? currentPassword,
    String? newPassword,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [currentPassword, newPassword]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<String?> getRememberedEmail() =>
      (super.noSuchMethod(
            Invocation.method(#getRememberedEmail, []),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<bool> isRememberMeEnabled() =>
      (super.noSuchMethod(
            Invocation.method(#isRememberMeEnabled, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> hasValidSession() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidSession, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> updateSessionActivity() =>
      (super.noSuchMethod(
            Invocation.method(#updateSessionActivity, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void updateCurrentUser(_i8.UserModel? user) => super.noSuchMethod(
    Invocation.method(#updateCurrentUser, [user]),
    returnValueForMissingStub: null,
  );

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<bool> hasDocumentPermission(String? permission) =>
      (super.noSuchMethod(
            Invocation.method(#hasDocumentPermission, [permission]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> hasCategoryAccess(String? categoryId) =>
      (super.noSuchMethod(
            Invocation.method(#hasCategoryAccess, [categoryId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> hasSystemPermission(String? permission) =>
      (super.noSuchMethod(
            Invocation.method(#hasSystemPermission, [permission]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canPerformUnlimitedQueries() =>
      (super.noSuchMethod(
            Invocation.method(#canPerformUnlimitedQueries, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canAccessStorageManagement() =>
      (super.noSuchMethod(
            Invocation.method(#canAccessStorageManagement, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canManageUsers() =>
      (super.noSuchMethod(
            Invocation.method(#canManageUsers, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canViewAnalytics() =>
      (super.noSuchMethod(
            Invocation.method(#canViewAnalytics, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canUploadFiles() =>
      (super.noSuchMethod(
            Invocation.method(#canUploadFiles, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canDeleteFiles() =>
      (super.noSuchMethod(
            Invocation.method(#canDeleteFiles, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> canApproveFiles() =>
      (super.noSuchMethod(
            Invocation.method(#canApproveFiles, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<Map<String, dynamic>> getCurrentUserPermissionSummary() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUserPermissionSummary, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> refreshCurrentUserPermissions() =>
      (super.noSuchMethod(
            Invocation.method(#refreshCurrentUserPermissions, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> hasDocumentAccess(String? documentId, String? action) =>
      (super.noSuchMethod(
            Invocation.method(#hasDocumentAccess, [documentId, action]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
